import { create } from 'zustand';
import { 
  collection, 
  addDoc, 
  getDocs, 
  getDoc, 
  doc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy 
} from 'firebase/firestore';
import { firestore } from '@/config/firebase';

// It's good practice to define types for complex objects like AI plan items
interface DietRecommendationItem {
  week: number;
  stage: 'early' | 'mid' | 'late';
  title: string;
  description: string;
  nutrients: string[];
  foods: string[];
}

interface HealthRecommendationItem {
  week: number;
  stage: 'early' | 'mid' | 'late';
  title: string;
  description: string;
  checkups?: string[];
  treatments?: string[];
}
export interface Pregnancy {
  id: string;
  farmId: string;
  animalId: string;
  animalName: string;
  species: string;
  sireId: string;
  sireName: string;
  conceptionDate: string;
  expectedDate?: string;
  daysRemaining?: string;
  status: 'confirmed' | 'suspected' | 'not_pregnant';
  progress?: number;
  stage?: 'early' | 'mid' | 'late';
  notes?: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  animalImageUri?: string; // Add image URI field
  isAICross?: boolean; // Add AI Cross field
    // Add the missing properties:
    useAIPlans?: boolean;
    aiGeneratedDietPlan?: DietRecommendationItem[];
    aiGeneratedHealthPlan?: HealthRecommendationItem[];
}

interface PregnancyState {
  pregnancies: Pregnancy[];
  loading: boolean;
  error: string | null;

  fetchPregnancies: (farmId: string) => Promise<void>;
  getPregnancy: (id: string) => Promise<Pregnancy | null>;
  addPregnancy: (farmId: string, animalId: string, data: any) => Promise<string>;
  updatePregnancy: (id: string, data: Partial<Pregnancy>) => Promise<void>;
  deletePregnancy: (id: string) => Promise<void>;
  clearPregnancies: () => void;
}

export const usePregnancyStore = create<PregnancyState>((set, get) => ({
  pregnancies: [],
  loading: false,
  error: null,
  
  fetchPregnancies: async (farmId: string) => {
    try {
      set({ loading: true, error: null });
      
      
      let pregnanciesData: Pregnancy[] = [];
      const processedAnimalIds = new Set(); // Track processed animal IDs to avoid duplicates
      
      // Get all animals for this farm
      const animalsRef = collection(firestore, 'farms', farmId, 'animals');
      const animalsSnapshot = await getDocs(animalsRef);
      
      if (animalsSnapshot.empty) {
        set({ pregnancies: [], loading: false });
        return;
      }
      
      // Create a map of animal ID to animal data (including imageUri)
      const animalsMap = new Map();
      animalsSnapshot.forEach(doc => {
        const animalData = doc.data();
        animalsMap.set(doc.id, animalData);
      });
      
      
      // For each animal, fetch its pregnancies
      for (const animalDoc of animalsSnapshot.docs) {
        const animalId = animalDoc.id;
        const animalData = animalDoc.data();
        
        // Fetch pregnancies for this animal
        const pregnanciesRef = collection(firestore, 'farms', farmId, 'animals', animalId, 'pregnancies');
        const pregnanciesSnapshot = await getDocs(pregnanciesRef);
        
        if (!pregnanciesSnapshot.empty) {
          // Get all pregnancies for this animal
          const animalPregnancies: Pregnancy[] = [];
          
          pregnanciesSnapshot.forEach(doc => {
            const data = doc.data() as Omit<Pregnancy, 'id'>;
            // Calculate days remaining and progress if conception date is available
            let daysRemaining = '';
            let progress = 0;
            let stage = 'early';
            let expectedDate = '';
            
            if (data.conceptionDate) {
              const conceptionDate = new Date(data.conceptionDate);
              const today = new Date();
              
              // Get gestation period based on species
              const species = data.species?.toLowerCase();
              const gestationPeriod = species === 'cow' ? 280 :
                                         species === 'goat' ? 150 : 280;
              
              // Calculate expected date
              const expectedDateObj = new Date(conceptionDate);
              expectedDateObj.setDate(conceptionDate.getDate() + gestationPeriod);
              expectedDate = expectedDateObj.toISOString();
              
              // Calculate days remaining
              const diffTime = expectedDateObj.getTime() - today.getTime();
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
              daysRemaining = diffDays > 0 ? diffDays.toString() : '0';
              
              // Calculate progress (0-100%)
              const totalDays = gestationPeriod;
              const daysPassed = totalDays - diffDays;
              progress = Math.min(100, Math.max(0, (daysPassed / totalDays) * 100));
              
              // Determine stage
              if (progress < 33) {
                stage = 'early';
              } else if (progress < 66) {
                stage = 'mid';
              } else {
                stage = 'late';
              }
            }
            
            animalPregnancies.push({
              id: doc.id,
              ...data,
              daysRemaining,
              progress,
              stage,
              expectedDate: expectedDate || data.expectedDate,
              animalImageUri: animalData.imageUri // Add the animal's image URI
            } as Pregnancy);
          });
          
          // Sort pregnancies by creation date (newest first)
          animalPregnancies.sort((a, b) => {
            const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
            const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
            return dateB - dateA;
          });
          pregnanciesData.push(...animalPregnancies);
        }
      }
      

      if (pregnanciesData.length > 0) {
        console.log(':');
      }
      
      set({ pregnancies: pregnanciesData, loading: false });
    } catch (error) {
      console.error('Error fetching pregnancies:', error);
      set({ 
        error: (error as Error).message, 
        loading: false 
      });
    }
  },
  
  getPregnancy: async (id: string) => {
    try {
      // First try to get it from the store
      const storePregnancy = get().pregnancies.find(p => p.id === id);
      if (storePregnancy) {
        return storePregnancy;
      }
      
      // Try to get it from the top-level collection (for backward compatibility)
      const docRef = doc(firestore, 'pregnancies', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data() as Omit<Pregnancy, 'id'>;
        return { id: docSnap.id, ...data } as Pregnancy;
      }
      
      // If not found in top-level collection, search in all farms and animals
      
      // Get all farms
      const farmsRef = collection(firestore, 'farms');
      const farmsSnapshot = await getDocs(farmsRef);
      
      for (const farmDoc of farmsSnapshot.docs) {
        const farmId = farmDoc.id;
        
        // Get all animals in this farm
        const animalsRef = collection(firestore, 'farms', farmId, 'animals');
        const animalsSnapshot = await getDocs(animalsRef);
        
        for (const animalDoc of animalsSnapshot.docs) {
          const animalId = animalDoc.id;
          const animalData = animalDoc.data();
          
          // Check if this animal has the pregnancy we're looking for
          const pregnancyRef = doc(firestore, 'farms', farmId, 'animals', animalId, 'pregnancies', id);
          const pregnancySnap = await getDoc(pregnancyRef);
          
          if (pregnancySnap.exists()) {
            
            const data = pregnancySnap.data() as Omit<Pregnancy, 'id'>;
            
            // Calculate additional fields like days remaining, progress, etc.
            let daysRemaining = '';
            let progress = 0;
            let stage = 'early';
            let expectedDate = '';
            
            if (data.conceptionDate) {
              const conceptionDate = new Date(data.conceptionDate);
              const today = new Date();
              
              // Get gestation period based on species
              const species = data.species?.toLowerCase();
              const gestationPeriod = species === 'cow' ? 280 :
                                         species === 'goat' ? 150 : 280;
              
              // Calculate expected date
              const expectedDateObj = new Date(conceptionDate);
              expectedDateObj.setDate(conceptionDate.getDate() + gestationPeriod);
              expectedDate = expectedDateObj.toISOString();
              
              // Calculate days remaining
              const diffTime = expectedDateObj.getTime() - today.getTime();
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
              daysRemaining = diffDays > 0 ? diffDays.toString() : '0';
              
              // Calculate progress (0-100%)
              const totalDays = gestationPeriod;
              const daysPassed = totalDays - diffDays;
              progress = Math.min(100, Math.max(0, (daysPassed / totalDays) * 100));
              
              // Determine stage
              if (progress < 33) {
                stage = 'early';
              } else if (progress < 66) {
                stage = 'mid';
              } else {
                stage = 'late';
              }
            }
            
            return {
              id,
              ...data,
              daysRemaining,
              progress,
              stage,
              expectedDate: expectedDate || data.expectedDate,
              animalImageUri: animalData.imageUri
            } as Pregnancy;
          }
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error getting pregnancy:', error);
      return null;
    }
  },
  
  addPregnancy: async (farmId: string, animalId: string, data: any) => {
    try {
      set({ loading: true, error: null });


      // First, verify that the animal exists
      const animalRef = doc(firestore, 'farms', farmId, 'animals', animalId);
      const animalSnap = await getDoc(animalRef);

      if (!animalSnap.exists()) {
        throw new Error('Animal not found');
      }

      // Check for existing active pregnancies
      const pregnanciesRef = collection(firestore, 'farms', farmId, 'animals', animalId, 'pregnancies');
      const pregnanciesSnapshot = await getDocs(pregnanciesRef);

      // Check if there's an ongoing pregnancy
      let hasActivePregnancy = false;
      pregnanciesSnapshot.forEach(doc => {
        const pregnancyData = doc.data();
        if (pregnancyData.status === 'confirmed' || pregnancyData.status === 'suspected') {
          // Check if pregnancy is still ongoing (not completed)
          if (pregnancyData.conceptionDate) {
            const conceptionDate = new Date(pregnancyData.conceptionDate);
            const today = new Date();
            const daysPassed = Math.floor((today.getTime() - conceptionDate.getTime()) / (1000 * 60 * 60 * 24));

            // Get gestation period based on species
            const species = pregnancyData.species?.toLowerCase();
            const gestationPeriod = species === 'cow' ? 280 :
                                   species === 'goat' ? 150 :
                                   species === 'sheep' ? 147 :
                                   species === 'pig' ? 114 : 280;

            // If pregnancy is still within gestation period + 30 days buffer, consider it active
            if (daysPassed < gestationPeriod + 30) {
              hasActivePregnancy = true;
            }
          }
        }
      });

      if (hasActivePregnancy) {
        throw new Error('This animal already has an ongoing pregnancy. Please wait until the current pregnancy is completed before adding a new one.');
      }
      
      // Ensure required fields are present
      if (!data.createdBy) {
        // Import auth store dynamically to avoid circular dependency
        const { useAuthStore } = require('@/store/auth-store');
        const authStore = useAuthStore.getState();
        data.createdBy = authStore.user?.uid || authStore.user?.id || 'unknown';
      }
      
      // Add pregnancy record to the subcollection
      // Path: farms/{farmId}/animals/{animalId}/pregnancies/{pregnancyId}
      const pregnancyRef = collection(firestore, 'farms', farmId, 'animals', animalId, 'pregnancies');

      const pregnancyDataToSave = {
        ...data,
        farmId,
        animalId,
        createdAt: new Date().toISOString()
      };


      const docRef = await addDoc(pregnancyRef, pregnancyDataToSave);
      
      // Update the animal's pregnancy status
      const updatedAnimalRef = doc(firestore, 'farms', farmId, 'animals', animalId);
      await updateDoc(updatedAnimalRef, {
        pregnancyStatus: data.status,
        pregnancyId: docRef.id,
        updatedAt: new Date().toISOString()
      });
      
      
      // Refresh pregnancies list
      await get().fetchPregnancies(farmId);
      
      set({ loading: false });
      return docRef.id;
    } catch (error) {
      console.error('Error adding pregnancy:', error);
      set({ 
        error: (error as Error).message, 
        loading: false 
      });
      throw error;
    }
  },
  
  updatePregnancy: async (id: string, data: Partial<Pregnancy>) => {
    try {
      set({ loading: true, error: null });
      
      // For now, try to update in the top-level collection (for backward compatibility)
      const docRef = doc(firestore, 'pregnancies', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        await updateDoc(docRef, {
          ...data,
          updatedAt: new Date().toISOString()
        });
        
        // If we have farmId, refresh the pregnancies list
        if (data.farmId) {
          await get().fetchPregnancies(data.farmId);
        }
        
        set({ loading: false });
        return;
      }
      
      // If not found in top-level collection, it might be in the new structure
      // We would need farm ID and animal ID to find it
      // This would require a more complex query or maintaining an index
      
      throw new Error('Pregnancy not found');
    } catch (error) {
      console.error('Error updating pregnancy:', error);
      set({ 
        error: (error as Error).message, 
        loading: false 
      });
      throw error;
    }
  },
  
  deletePregnancy: async (id: string) => {
    try {
      set({ loading: true, error: null });
      
      // For now, try to delete from the top-level collection (for backward compatibility)
      const docRef = doc(firestore, 'pregnancies', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data() as Pregnancy;
        
        // Update animal's pregnancy status
        if (data.farmId && data.animalId) {
          const animalRef = doc(firestore, 'farms', data.farmId, 'animals', data.animalId);
          await updateDoc(animalRef, {
            pregnancyStatus: null,
            pregnancyId: null,
            updatedAt: new Date().toISOString()
          });
        }
        
        // Delete the pregnancy record
        await deleteDoc(docRef);
        
        // Refresh pregnancies list
        if (data.farmId) {
          await get().fetchPregnancies(data.farmId);
        }
        
        set({ loading: false });
        return;
      }
      
      // If not found in top-level collection, it might be in the new structure
      // We would need farm ID and animal ID to find it
      // This would require a more complex query or maintaining an index
      
      throw new Error('Pregnancy not found');
    } catch (error) {
      console.error('Error deleting pregnancy:', error);
      set({ 
        error: (error as Error).message, 
        loading: false 
      });
      throw error;
    }
  },

  clearPregnancies: () => {
    set({ pregnancies: [], error: null });
  }
}));
